import { StyleSheet } from 'react-native';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import fonts from 'themes/fonts';
import metrics from 'themes/metrics';

const styles = StyleSheet.create({
  container: {
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: metrics.spacing3,
    paddingVertical: metrics.spacing2,
    flexDirection: 'row',
    flex: 1,
  },
  postFullContainer: {
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    backgroundColor: 'white',
    flexDirection: 'column',
    flex: 1,
    borderRadius: metrics.radius4,
    height: metrics.fullHeight / 4.5,
  },
  mainContent: {
    minWidth: '100%',
    paddingHorizontal: metrics.spacing4,
    paddingVertical: metrics.spacing4,
    borderRadius: metrics.radius4,
  },
  topCard: {
    paddingHorizontal: metrics.spacing4,
    paddingVertical: metrics.spacing4,
  },
  postFullContent: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  imageContainer: {
    resizeMode: 'cover',
    borderRadius: metrics.radius4,
  },
  border: {
    borderRadius: metrics.radius4,
  },
  title: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors['Moss/400'],
  },
  whiteTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'white',
  },
  postImage: {
    width: metrics.fullWidth / 3 - metrics.spacing4,
    height: metrics.fullWidth / 3 - metrics.spacing4,
    borderRadius: metrics.radius2,
  },
  content: {
    height: metrics.fullWidth / 3 - metrics.spacing4,
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    flex: 1,
    paddingLeft: metrics.spacing3,
  },
  hintTitle: {
    color: colors['Grayiron/600'],
    fontSize: fonts.size.medium,
  },
  avatarTitle: {
    color: colors['Grayiron/600'],
    fontSize: fonts.size.medium,
    marginLeft: metrics.spacing2,
  },
  whiteHintTitle: {
    color: 'white',
    fontSize: fonts.size.medium,
    marginLeft: metrics.spacing2,
  },
  row: {
    ...commonStyles.rowCenter,
    justifyContent: 'flex-start',
  },
  rowPaddingRight: {
    ...commonStyles.rowCenter,
    justifyContent: 'flex-start',
    paddingRight: metrics.spacing4,
  },
  paddingTop2: {
    paddingTop: metrics.spacing2,
  },
  tagContainer: {
    alignItems: 'center', 
    justifyContent: 'center',
  },
  rowBetween: {
    flexDirection: 'row', 
    justifyContent: 'space-between', 
    alignItems: 'center', 
    width: '100%',
  },
});

export default styles;