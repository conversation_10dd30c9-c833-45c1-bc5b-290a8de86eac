import React, { useState, useRef, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Animated,
  Alert,
} from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Ionicons from 'react-native-vector-icons/Ionicons';
import colors from 'themes/colors';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import { REGEX_INPUT } from 'constants/authentication';
import { getChangeEmailKey, updateEmail } from 'api/users';
import Toast from 'react-native-toast-message';

interface ChangeEmailModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: (newEmail: string) => void;
  currentEmail: string;
}

interface EmailFormData {
  newEmail: string;
  key: string;
}

const emailSchema = yup.object({
  newEmail: yup
    .string()
    .required('<PERSON>ail mới là bắt buộc')
    .trim()
    .matches(REGEX_INPUT.EMAIL, 'Định dạng email không hợp lệ'),
});

const keySchema = yup.object({
  key: yup.string().required('Mã xác nhận là bắt buộc').trim(),
});

const ChangeEmailModal: React.FC<ChangeEmailModalProps> = ({
  visible,
  onClose,
  onSuccess,
  currentEmail,
}) => {
  const [step, setStep] = useState<'email' | 'key'>('email');
  const [loading, setLoading] = useState(false);
  const [newEmail, setNewEmail] = useState('');
  const [countdown, setCountdown] = useState(0);
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  const emailForm = useForm<{ newEmail: string }>({
    resolver: yupResolver(emailSchema),
    defaultValues: { newEmail: '' },
  });

  const keyForm = useForm<{ key: string }>({
    resolver: yupResolver(keySchema),
    defaultValues: { key: '' },
  });

  useEffect(() => {
    if (visible) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }).start();
    } else {
      scaleAnim.setValue(0.9);
      setStep('email');
      setNewEmail('');
      setCountdown(0);
      emailForm.reset();
      keyForm.reset();
    }
  }, [visible, scaleAnim, emailForm, keyForm]);

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [countdown]);

  const handleSendKey = async (data: { newEmail: string }) => {
    if (data.newEmail === currentEmail) {
      Toast.show({
        type: 'error',
        text1: 'Email mới không được trùng với email hiện tại',
      });
      return;
    }

    setLoading(true);
    try {
      await getChangeEmailKey({ body: { newEmail: data.newEmail } });
      setNewEmail(data.newEmail);
      setStep('key');
      setCountdown(60);
      Toast.show({
        type: 'success',
        text1: 'Mã xác nhận đã được gửi đến email mới',
      });
    } catch (error: any) {
      console.log("in ra lỗi mã xác nhận là: ", error)
      Toast.show({
        type: 'error',
        text1: error?.message || 'Không thể gửi mã xác nhận',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyKey = async (data: { key: string }) => {
    setLoading(true);
    try {
      await updateEmail({ body: { newEmail, key: data.key } });
      Toast.show({
        type: 'success',
        text1: 'Cập nhật email thành công',
      });
      onSuccess(newEmail);
      onClose();
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: error?.message || 'Mã xác nhận không đúng',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleResendKey = async () => {
    if (countdown > 0) return;
    
    setLoading(true);
    try {
      await getChangeEmailKey({ body: { newEmail } });
      setCountdown(60);
      Toast.show({
        type: 'success',
        text1: 'Mã xác nhận đã được gửi lại',
      });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: error?.message || 'Không thể gửi lại mã xác nhận',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View style={emailModalStyles.overlay}>
        <Animated.View
          style={[
            emailModalStyles.container,
            { transform: [{ scale: scaleAnim }] },
          ]}
        >
          <View style={emailModalStyles.header}>
            <Text style={emailModalStyles.title}>
              {step === 'email' ? 'Thay đổi email' : 'Xác nhận email mới'}
            </Text>
            <TouchableOpacity
              onPress={handleClose}
              style={emailModalStyles.closeButton}
              disabled={loading}
            >
              <Ionicons name="close" size={24} color={colors['Gray/600']} />
            </TouchableOpacity>
          </View>

          {step === 'email' ? (
            <View style={emailModalStyles.content}>
              <Text style={emailModalStyles.description}>
                Email hiện tại: <Text style={emailModalStyles.currentEmail}>{currentEmail}</Text>
              </Text>
              <Text style={emailModalStyles.description}>
                Nhập email mới để nhận mã xác nhận:
              </Text>

              <Controller
                control={emailForm.control}
                name="newEmail"
                render={({ field: { onChange, onBlur, value } }) => (
                  <View style={emailModalStyles.inputContainer}>
                    <TextInput
                      style={emailModalStyles.input}
                      placeholder="Nhập email mới"
                      placeholderTextColor={colors['Gray/400']}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      editable={!loading}
                    />
                  </View>
                )}
              />
              {emailForm.formState.errors.newEmail && (
                <Text style={emailModalStyles.errorText}>
                  {emailForm.formState.errors.newEmail.message}
                </Text>
              )}

              <View style={emailModalStyles.buttonContainer}>
                <TouchableOpacity
                  style={[emailModalStyles.button, emailModalStyles.cancelButton]}
                  onPress={handleClose}
                  disabled={loading}
                >
                  <Text style={emailModalStyles.cancelButtonText}>Hủy</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[emailModalStyles.button, emailModalStyles.primaryButton, loading && emailModalStyles.disabledButton]}
                  onPress={emailForm.handleSubmit(handleSendKey)}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator size="small" color={colors.white} />
                  ) : (
                    <Text style={emailModalStyles.primaryButtonText}>Gửi mã</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View style={emailModalStyles.content}>
              <Text style={emailModalStyles.description}>
                Mã xác nhận đã được gửi đến: <Text style={emailModalStyles.newEmail}>{newEmail}</Text>
              </Text>
              <Text style={emailModalStyles.description}>
                Vui lòng kiểm tra email và nhập mã xác nhận:
              </Text>

              <Controller
                control={keyForm.control}
                name="key"
                render={({ field: { onChange, onBlur, value } }) => (
                  <View style={emailModalStyles.inputContainer}>
                    <TextInput
                      style={emailModalStyles.input}
                      placeholder="Nhập mã xác nhận"
                      placeholderTextColor={colors['Gray/400']}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      autoCapitalize="characters"
                      editable={!loading}
                    />
                  </View>
                )}
              />
              {keyForm.formState.errors.key && (
                <Text style={emailModalStyles.errorText}>
                  {keyForm.formState.errors.key.message}
                </Text>
              )}

              <TouchableOpacity
                style={[emailModalStyles.resendButton, countdown > 0 && emailModalStyles.disabledButton]}
                onPress={handleResendKey}
                disabled={countdown > 0 || loading}
              >
                <Text style={[emailModalStyles.resendButtonText, countdown > 0 && emailModalStyles.disabledText]}>
                  {countdown > 0 ? `Gửi lại sau ${countdown}s` : 'Gửi lại mã'}
                </Text>
              </TouchableOpacity>

              <View style={emailModalStyles.buttonContainer}>
                <TouchableOpacity
                  style={[emailModalStyles.button, emailModalStyles.cancelButton]}
                  onPress={() => setStep('email')}
                  disabled={loading}
                >
                  <Text style={emailModalStyles.cancelButtonText}>Quay lại</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[emailModalStyles.button, emailModalStyles.primaryButton, loading && emailModalStyles.disabledButton]}
                  onPress={keyForm.handleSubmit(handleVerifyKey)}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator size="small" color={colors.white} />
                  ) : (
                    <Text style={emailModalStyles.primaryButtonText}>Xác nhận</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          )}
        </Animated.View>
      </View>
    </Modal>
  );
};

const emailModalStyles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: metrics.spacing4,
  },
  container: {
    backgroundColor: colors.white,
    borderRadius: metrics.radius4,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: metrics.spacing4,
    borderBottomWidth: 1,
    borderBottomColor: colors['Gray/200'],
  },
  title: {
    ...fonts.style.h3,
    fontWeight: '600',
    color: colors['Gray/800'],
  },
  closeButton: {
    padding: metrics.spacing1,
  },
  content: {
    padding: metrics.spacing4,
  },
  description: {
    ...fonts.style.normal,
    color: colors['Gray/600'],
    marginBottom: metrics.spacing3,
    lineHeight: 20,
  },
  currentEmail: {
    fontWeight: '600',
    color: colors['Primary/600'],
  },
  newEmail: {
    fontWeight: '600',
    color: colors['Success/600'],
  },
  inputContainer: {
    marginBottom: metrics.spacing2,
  },
  input: {
    borderWidth: 1,
    borderColor: colors['Gray/300'],
    borderRadius: metrics.radius3,
    padding: metrics.spacing3,
    ...fonts.style.normal,
    color: colors['Gray/800'],
    backgroundColor: colors.white,
  },
  errorText: {
    ...fonts.style.description,
    color: colors['Danger/600'],
    marginBottom: metrics.spacing2,
  },
  resendButton: {
    alignSelf: 'flex-end',
    marginBottom: metrics.spacing3,
  },
  resendButtonText: {
    ...fonts.style.description,
    color: colors['Primary/600'],
    fontWeight: '500',
  },
  disabledText: {
    color: colors['Gray/400'],
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: metrics.spacing2,
  },
  button: {
    flex: 1,
    paddingVertical: metrics.spacing3,
    borderRadius: metrics.radius3,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: colors['Gray/100'],
  },
  cancelButtonText: {
    ...fonts.style.normal,
    color: colors['Gray/700'],
    fontWeight: '500',
  },
  primaryButton: {
    backgroundColor: colors['Primary/500'],
  },
  primaryButtonText: {
    ...fonts.style.normal,
    color: colors.white,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.6,
  },
});

export default ChangeEmailModal;
