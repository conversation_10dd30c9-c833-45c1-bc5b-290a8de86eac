import React, { useState, useRef } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  View,
  Text,
  TextInput,
  ActivityIndicator,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import metrics from 'themes/metrics';
import colors from 'themes/colors';
import fonts from 'themes/fonts';
import { useDeleteAccount } from './hooks';
import HomeHeader from 'components/Header/HomeHeader';
import CustomAlert from 'utils/widgets/CustomAlert';

type RootStackParamList = {
  DeleteAccount: undefined;
  Login: undefined;
};

type NavigationProps = NativeStackNavigationProp<RootStackParamList>;

interface AlertState {
  visible: boolean;
  type: 'success' | 'error' | 'confirm';
  title: string;
  message?: string;
  buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[];
}

const DeleteAccount: React.FC = () => {
  const { deleteKey, loading, error, deleteAccount, countdown, canResend, resendOtp, userEmail } = useDeleteAccount();
  const navigation = useNavigation<NavigationProps>();
  const [verificationCode, setVerificationCode] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [alert, setAlert] = useState<AlertState | null>(null);
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const deleteButtonAnim = useRef(new Animated.Value(1)).current;
  const resendButtonAnim = useRef(new Animated.Value(1)).current;

  React.useEffect(() => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 6,
      tension: 60,
      useNativeDriver: true,
    }).start();
  }, [scaleAnim]);

  const handleButtonPressIn = (anim: Animated.Value) => {
    Animated.spring(anim, {
      toValue: 0.92,
      friction: 6,
      tension: 60,
      useNativeDriver: true,
    }).start();
  };

  const handleButtonPressOut = (anim: Animated.Value) => {
    Animated.spring(anim, {
      toValue: 1,
      friction: 6,
      tension: 60,
      useNativeDriver: true,
    }).start();
  };

  const showAlert = (alertState: AlertState) => {
    setAlert(alertState);
  };

  const hideAlert = () => {
    setAlert(null);
  };

  const handleDelete = () => {
    if (!deleteKey) {
      showAlert({
        visible: true,
        type: 'error',
        title: 'Lỗi',
        message: 'Không thể lấy khóa xóa tài khoản',
        buttons: [{ text: 'Đóng', onPress: hideAlert }],
      });
      return;
    }

    if (!verificationCode || verificationCode.length !== 8) {
      showAlert({
        visible: true,
        type: 'error',
        title: 'Lỗi',
        message: 'Vui lòng nhập mã xác nhận gồm 8 ký tự',
        buttons: [{ text: 'Đóng', onPress: hideAlert }],
      });
      return;
    }

    if (verificationCode !== deleteKey) {
      showAlert({
        visible: true,
        type: 'error',
        title: 'Lỗi',
        message: 'Mã xác nhận không đúng',
        buttons: [{ text: 'Đóng', onPress: hideAlert }],
      });
      return;
    }

    showAlert({
      visible: true,
      type: 'confirm',
      title: 'Xác nhận xóa tài khoản',
      message: 'Hành động này không thể hoàn tác. Bạn có chắc chắn muốn xóa tài khoản?',
      buttons: [
        { text: 'Hủy', style: 'cancel', onPress: hideAlert },
        { text: 'Xóa', style: 'destructive', onPress: handleConfirmDelete },
      ],
    });
  };

  const handleConfirmDelete = async () => {
    hideAlert();
    setIsDeleting(true);
    try {
      await deleteAccount({ body: { deletedKey: deleteKey } });
      showAlert({
        visible: true,
        type: 'success',
        title: 'Thành công',
        message: 'Tài khoản đã được xóa thành công!',
        buttons: [{ text: 'Đăng nhập', onPress: () => navigation.navigate('Login') }],
      });
    } catch (err) {
      showAlert({
        visible: true,
        type: 'error',
        title: 'Lỗi',
        message: 'Không thể xóa tài khoản. Vui lòng thử lại.',
        buttons: [{ text: 'Đóng', onPress: hideAlert }],
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleResendCode = () => {
    if (canResend) {
      resendOtp();
      setVerificationCode('');
    } else {
      showAlert({
        visible: true,
        type: 'confirm',
        title: 'Thông báo',
        message: `Vui lòng chờ ${countdown} giây để gửi lại mã xác nhận.`,
        buttons: [{ text: 'Đóng', onPress: hideAlert }],
      });
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors['Primary/500']} />
        <Text style={styles.loadingText}>Đang tải...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons
          name="alert-circle-outline"
          size={40}
          color={colors['Danger/500']}
        />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <Text style={styles.retryButtonText}>Quay lại</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.safeAreaView}>
      <HomeHeader backButton 
      title='Xóa tài khoản' noStretch />
      <View style={styles.contentContainer}>
        <Animated.View style={[styles.card, { transform: [{ scale: scaleAnim }] }]}>
          <MaterialCommunityIcons
            name="lock-outline"
            size={36}
            color={colors['Primary/600']}
            style={styles.icon}
          />
          <Text style={styles.title}>Xóa tài khoản</Text>
          <Text style={styles.description}>
            Mã xác nhận gồm 8 ký tự ngẫu nhiên đã được gửi đến email:
          </Text>
          {userEmail && (
            <Text style={styles.emailText}>
              {userEmail}
            </Text>
          )}
          <Text style={styles.subDescription}>
            Vui lòng kiểm tra email và nhập mã xác nhận để tiếp tục.
          </Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.codeInput}
              value={verificationCode}
              onChangeText={setVerificationCode}
              placeholder="Nhập mã xác nhận"
              placeholderTextColor={colors['Gray/400']}
              maxLength={8}
              autoFocus
              autoCapitalize="characters"
              accessibilityLabel="Verification code input"
            />
          </View>
          <Animated.View style={{ transform: [{ scale: deleteButtonAnim }] }}>
            <TouchableOpacity
              style={[styles.deleteButton, isDeleting && styles.disabledButton]}
              onPress={handleDelete}
              disabled={isDeleting}
              onPressIn={() => handleButtonPressIn(deleteButtonAnim)}
              onPressOut={() => handleButtonPressOut(deleteButtonAnim)}
              activeOpacity={0.7}
              accessibilityRole="button"
              accessibilityLabel="Delete account"
            >
              <Text style={styles.deleteButtonText}>
                {isDeleting ? 'Đang xóa...' : 'Xóa tài khoản'}
              </Text>
            </TouchableOpacity>
          </Animated.View>
          <Animated.View style={{ transform: [{ scale: resendButtonAnim }] }}>
            <TouchableOpacity
              style={[styles.resendButton, !canResend && styles.disabledButton]}
              onPress={handleResendCode}
              disabled={!canResend}
              onPressIn={() => handleButtonPressIn(resendButtonAnim)}
              onPressOut={() => handleButtonPressOut(resendButtonAnim)}
              activeOpacity={0.7}
              accessibilityRole="button"
              accessibilityLabel="Resend verification code"
            >
              <Text style={styles.resendButtonText}>
                {canResend ? 'Gửi lại mã' : `Gửi lại (${countdown}s)`}
              </Text>
            </TouchableOpacity>
          </Animated.View>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => navigation.goBack()}
            activeOpacity={0.7}
            accessibilityRole="button"
            accessibilityLabel="Cancel"
          >
            <Text style={styles.cancelButtonText}>Hủy</Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
      {alert && (
        <CustomAlert
          visible={alert.visible}
          type={alert.type}
          title={alert.title}
          message={alert.message}
          buttons={alert.buttons}
          onDismiss={hideAlert}
          dismissable
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    flex: 1,
    backgroundColor: colors['Gray/50'],
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: metrics.spacing3,
    paddingVertical: metrics.spacing4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  card: {
    backgroundColor: colors.white,
    borderRadius: metrics.radius3,
    padding: metrics.spacing3,
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
    shadowColor: colors['Gray/700'],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  icon: {
    marginBottom: metrics.spacing2,
  },
  title: {
    ...fonts.style.h3,
    fontWeight: '600',
    color: colors['Gray/800'],
    marginBottom: metrics.spacing1,
  },
  description: {
     ...fonts.style.normal,
    color: colors['Gray/600'],
    textAlign: 'center',
    marginBottom: metrics.spacing2,
    lineHeight: 20,
  },
  emailText: {
    ...fonts.style.normal,
    color: colors['Primary/600'],
    textAlign: 'center',
    fontWeight: '600',
    marginBottom: metrics.spacing2,
    backgroundColor: colors['Primary/50'],
    paddingHorizontal: metrics.spacing2,
    paddingVertical: metrics.spacing1,
    borderRadius: metrics.radius2,
  },
  subDescription: {
    ...fonts.style.description,
    color: colors['Gray/500'],
    textAlign: 'center',
    marginBottom: metrics.spacing3,
    lineHeight: 18,
  },
  inputContainer: {
    width: '100%',
    backgroundColor: colors['Gray/50'],
    borderRadius: metrics.radius2,
    borderWidth: 1,
    borderColor: colors['Gray/200'],
    marginBottom: metrics.spacing3,
    paddingHorizontal: metrics.spacing2,
  },
  codeInput: {
     ...fonts.style.normal,
    color: colors['Gray/800'],
    paddingVertical: metrics.spacing2,
    textAlign: 'center',
    fontSize: 18,
    fontWeight: '600',
    letterSpacing: 2,
  },
  otpInput: {
     ...fonts.style.normal,
    color: colors['Gray/800'],
    paddingVertical: metrics.spacing2,
  },
  deleteButton: {
    backgroundColor: colors['Danger/600'],
    paddingVertical: metrics.spacing2,
    borderRadius: metrics.radius2,
    width: '100%',
    alignItems: 'center',
    marginBottom: metrics.spacing2,
  },
  deleteButtonText: {
     ...fonts.style.normal,
    color: colors.white,
    fontWeight: '600',
  },
  disabledButton: {
    backgroundColor: colors['Danger/400'],
    opacity: 0.6,
  },
  resendButton: {
    backgroundColor: colors['Primary/500'],
    paddingVertical: metrics.spacing2,
    borderRadius: metrics.radius2,
    width: '100%',
    alignItems: 'center',
    marginBottom: metrics.spacing2,
  },
  resendButtonText: {
     ...fonts.style.normal,
    color: colors.white,
    fontWeight: '600',
  },
  cancelButton: {
    paddingVertical: metrics.spacing2,
    borderRadius: metrics.radius2,
    width: '100%',
    alignItems: 'center',
  },
  cancelButtonText: {
     ...fonts.style.normal,
    color: colors['Primary/600'],
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors['Gray/50'],
  },
  loadingText: {
     ...fonts.style.normal,
    color: colors['Gray/600'],
    marginTop: metrics.spacing2,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: metrics.spacing3,
    backgroundColor: colors['Gray/50'],
  },
  errorText: {
     ...fonts.style.normal,
    color: colors['Danger/500'],
    textAlign: 'center',
    marginVertical: metrics.spacing2,
  },
  retryButton: {
    backgroundColor: colors['Primary/50'],
    paddingVertical: metrics.spacing2,
    paddingHorizontal: metrics.spacing3,
    borderRadius: metrics.radius2,
    alignItems: 'center',
  },
  retryButtonText: {
     ...fonts.style.normal,
    color: colors['Primary/600'],
    fontWeight: '600',
  },
});

export default DeleteAccount;