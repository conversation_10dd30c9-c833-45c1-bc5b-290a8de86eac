import React, { useState } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import metrics from 'themes/metrics';
import colors from 'themes/colors';
import fonts from 'themes/fonts';
import HomeHeader from 'components/Header/HomeHeader';
import CustomAlert from 'utils/widgets/CustomAlert';

type RootStackParamList = {
  ConfirmDeleteAccount: undefined;
  DeleteAccount: undefined;
  AppTab: undefined;
};

type NavigationProps = NativeStackNavigationProp<RootStackParamList>;

const ConfirmDeleteAccount: React.FC = () => {
  const navigation = useNavigation<NavigationProps>();
  const [showConfirm, setShowConfirm] = useState(false);
  const scaleAnim = React.useRef(new Animated.Value(0.95)).current;

  React.useEffect(() => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 8,
      tension: 40,
      useNativeDriver: true,
    }).start();
  }, [scaleAnim]);

  const handleConfirm = () => {
    setShowConfirm(true);
  };

  const handleConfirmYes = () => {
    setShowConfirm(false);
    navigation.navigate('DeleteAccount');
  };

  return (
    <SafeAreaView style={styles.safeAreaView}>
      <HomeHeader backButton 
      title='Xóa tài khoản'
      noStretch />
      <View style={styles.contentContainer}>
        <LinearGradient
          colors={['rgba(255, 77, 77, 0.15)', 'transparent']}
          style={styles.gradientOverlay}
        />
        <Animated.View style={[styles.content, { transform: [{ scale: scaleAnim }] }]}>
          <MaterialCommunityIcons
            name="alert-circle-outline"
            size={70}  // Tăng kích thước icon
            color={colors['Danger/500']}
            style={styles.icon}
          />
          <Text style={styles.title}>Xác nhận xóa tài khoản</Text>
          <Text style={styles.description}>
            Bạn sắp xóa tài khoản của mình. Hành động này sẽ xóa vĩnh viễn tất cả dữ liệu liên quan. Nhấn "Tiếp tục" để nhận và nhập mã OTP.
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={handleConfirm}
              activeOpacity={0.8}
            >
              <MaterialCommunityIcons
                name="arrow-right"
                size={16}  
                color={colors.white}
                style={styles.buttonIcon}
              />
              <Text style={styles.deleteButtonText}>Tiếp tục</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
      <CustomAlert
        visible={showConfirm}
        type="confirm"
        title="Bạn có chắc chắn?"
        message="Xóa tài khoản là hành động không thể hoàn tác. Bạn sẽ cần nhập mã OTP để hoàn tất."
        buttons={[
          { text: 'Hủy', style: 'cancel', onPress: () => setShowConfirm(false) },
          { text: 'Tiếp tục', style: 'destructive', onPress: handleConfirmYes },
        ]}
        dismissable={true}
        onDismiss={() => setShowConfirm(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    flex: 1,
    backgroundColor: colors['Grayiron/50'],
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: metrics.spacing2,
    paddingVertical: metrics.spacing3,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors['Grayiron/50'],
  },
  gradientOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '50%',
  },
  content: {
    width: '90%',
    alignItems: 'center',
  },
  icon: {
    marginBottom: metrics.spacing3,
  },
  title: {
    ...fonts.style.h2,
    fontWeight: '700',
    color: colors['Grayiron/700'],
    textAlign: 'center',
    marginBottom: metrics.spacing2,
    lineHeight: 36,
    fontSize: 26,  // Tăng cỡ chữ tiêu đề
  },
  description: {
    ...fonts.style.description,
    color: colors['Grayiron/600'],
    textAlign: 'center',
    marginBottom: metrics.spacing4,
    lineHeight: 28,
    paddingHorizontal: metrics.spacing2,
    fontSize: 16,  // Tăng cỡ chữ mô tả
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
    gap: metrics.spacing2,
  },
  deleteButton: {
    flexDirection: 'row',
    backgroundColor: colors['Danger/500'],
    paddingVertical: metrics.spacing2,
    paddingHorizontal: metrics.spacing3,
    borderRadius: metrics.radius1,
    width: '90%',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors['Danger/600'],
    shadowColor: colors['Grayiron/700'],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  deleteButtonText: {
    ...fonts.style.normal,
    color: colors.white,
    fontWeight: '600',
    fontSize: 16,  // Tăng cỡ chữ nút
    marginLeft: metrics.spacing1,
  },
  cancelButton: {
    flexDirection: 'row',
    backgroundColor: colors['Moss/50'],
    paddingVertical: metrics.spacing2,
    paddingHorizontal: metrics.spacing3,
    borderRadius: metrics.radius1,
    width: '90%',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors['Moss/200'],
    shadowColor: colors['Grayiron/700'],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  cancelButtonText: {
    ...fonts.style.normal,
    color: colors['Moss/700'],
    fontWeight: '600',
    fontSize: 20,  // Tăng cỡ chữ nút
    marginLeft: metrics.spacing1,
  },
  buttonIcon: {
    marginRight: metrics.spacing1,
  },
});

export default ConfirmDeleteAccount;