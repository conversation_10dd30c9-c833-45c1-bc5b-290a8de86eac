import { useEffect, useState } from 'react';
import { getPostDetail } from 'api/posts';

// <PERSON><PERSON><PERSON> interface cho Post
interface Post {
  id: string | number;
  title: string;
  content: string;
  permalink?: string;
  author: string;
  createdAt: string;
  indexOfContent?: { heading: string; content: string }[];
  relatedPosts?: { id: number; title: string; author: string; thumbnail: string; topic?: { name: string } }[];
}

export const usePostDetail = ({ id }: { id?: string | number }) => {
  const [loading, setLoading] = useState(false);
  const [post, setPost] = useState<Post | null>(null);
  const [error, setError] = useState<string | null>(null);

  const fetchPost = async () => {
    if (!id) {
      setError('Không có ID bài viết');
      setPost(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      console.log('usePostDetail - Fetching post with ID:', id);
      const response = await getPostDetail({ id });
      setPost(response);
    } catch (error) {
      console.error('usePostDetail - Error fetching post:', error);
      setError('Không thể tải bài viết');
      setPost(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPost();
  }, [id]); 

  return {
    loading,
    post,
    error,
  };
};