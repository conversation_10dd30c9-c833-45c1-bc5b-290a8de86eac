import React, { useState, useCallback } from "react";
import {
  SafeAreaView,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  View,
  Text,
  Pressable,
  Image,
} from "react-native";
import { useNavigation, useRoute, useFocusEffect } from "@react-navigation/native";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import HomeHeader from "components/Header/HomeHeader";
import ProductGrid from "components/shared/ProductGrid";
import AddToCartModal from "components/shared/AddToCartModal";
import Toast from "react-native-toast-message";
import { addRecentProducts } from "utils/cart";
import { useMerchant } from "./hooks";
import { Product as ProductType } from "types/product";
import metrics from "themes/metrics";
import fonts from "themes/fonts";
import colors from "themes/colors";
import commonStyles from "themes/commonStyles";

const TABS = [
  { key: "all", title: "Tất cả sản phẩm", icon: "view-list" },
  { key: "newest", title: "<PERSON>ớ<PERSON> nhất", icon: "new-releases" },
  { key: "priceAsc", title: "<PERSON><PERSON><PERSON> thấp đến cao", icon: "arrow-upward" },
  { key: "priceDesc", title: "Giá cao đến thấp", icon: "arrow-downward" },
];

const MerchantScreen = () => {
  const route = useRoute();
  const navigation = useNavigation<any>();
  const merchantId = route.params?.merchantId as string;
  
  // States
  const [activeTab, setActiveTab] = useState(TABS[0].key);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<ProductType | null>(null);
  
  // Hooks
  const { merchant, products, loading, error, hasMore, loadMore, refresh, sortProducts } = useMerchant(merchantId);

  // Effects
  useFocusEffect(
    useCallback(() => {
      refresh();
    }, [refresh])
  );

  // Handlers
  const handleTabChange = (tabKey: string) => {
    setActiveTab(tabKey);
    sortProducts(tabKey as 'all' | 'popular' | 'newest' | 'priceAsc' | 'priceDesc');
  };

  const navigateToProductDetail = useCallback((productId: string) => {
    navigation.navigate("ProductDetail", { id: productId });
  }, [navigation]);

  const handleProductPress = useCallback((product: ProductType) => {
    if (product && product.id) {
      addRecentProducts(product);
      navigateToProductDetail(product.id);
    } else {
      console.error("Invalid product data for navigation", product);
      Toast.show({
        type: "error",
        text1: "Không thể mở sản phẩm",
        text2: "Vui lòng thử lại sau",
        position: "top",
      });
    }
  }, [navigateToProductDetail]);

  const openModal = (product: ProductType) => {
    setSelectedProduct(product);
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
    setSelectedProduct(null);
  };

  // Render functions
  const renderMerchantInfo = () => {
    if (!merchant) return null;
    
    return (
      <View style={styles.merchantInfo}>
        <Image
          source={{ uri: merchant.logo ?? "https://via.placeholder.com/80" }}
          style={styles.merchantLogo}
          onError={(e) => console.log("Merchant logo error:", e.nativeEvent.error)}
        />
        <View style={styles.merchantDetails}>
          <Text style={styles.merchantName}>{merchant.storeName || "Cửa hàng"}</Text>
          <Text style={styles.merchantAddress}>
            {[merchant.address, merchant.ward, merchant.district, merchant.city]
              .filter(Boolean)
              .join(", ")}
          </Text>
          <View style={styles.merchantStats}>
            <Text style={styles.merchantStat}>Rating: {merchant.rating}/5</Text>
            <Text style={styles.merchantStat}>Sản phẩm: {merchant.numOfTotalProducts}</Text>
            <Text style={styles.merchantStat}>Người theo dõi: {merchant.numOfFollowers}</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderTabs = () => (
    <View style={styles.tabContainer}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.tabScroll}>
        {TABS.map((tab) => (
          <Pressable
            key={tab.key}
            style={({ pressed }) => [
              styles.tab,
              activeTab === tab.key && styles.activeTab,
              pressed && styles.pressedTab,
            ]}
            onPress={() => handleTabChange(tab.key)}
            android_ripple={{ color: colors["Primary/200"], borderless: true }}
          >
            <MaterialIcons
              name={tab.icon}
              size={22}
              color={activeTab === tab.key ? colors.white : colors["Grayiron/700"]}
              style={styles.tabIcon}
            />
            <Text style={[styles.tabText, activeTab === tab.key && styles.activeTabText]}>
              {tab.title}
            </Text>
          </Pressable>
        ))}
      </ScrollView>
    </View>
  );

  const renderContent = () => {
    if (loading && !merchant && products.length === 0) {
      return (
        <View style={[styles.center, styles.outerWrapper]}>
          <ActivityIndicator size="large" color={colors["Primary/400"]} />
        </View>
      );
    }
    
    if (error && !merchant && products.length === 0) {
      return (
        <View style={[styles.center, styles.outerWrapper]}>
          <Text style={commonStyles.errorText}>{error}</Text>
        </View>
      );
    }
    
    return (
      <View style={styles.outerWrapper}>
        {renderMerchantInfo()}
        {renderTabs()}
        <ProductGrid
          products={products}
          loading={loading && products.length === 0}
          loadingMore={loading && hasMore}
          hasMore={hasMore}
          onLoadMore={loadMore}
          customStyles={{ paddingTop: 0 }}
          onProductPress={handleProductPress}
          onAddToCart={openModal}
        />
        <AddToCartModal
          visible={modalVisible}
          product={selectedProduct}
          onClose={closeModal}
        />
      </View>
    );
  };

  return (
    <>
      <HomeHeader backButton title="Thông tin cửa hàng" noStretch />
      <SafeAreaView style={styles.safeAreaView}>
        {renderContent()}
      </SafeAreaView>
      <Toast />
    </>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    flex: 1,
    backgroundColor: colors.white,
  },
  center: {
    ...commonStyles.rowCenter,
    flex: 1,
  },
  outerWrapper: {
    flex: 1,
    paddingTop: metrics.spacing4,
  },
  merchantInfo: {
    flexDirection: 'row',
    padding: metrics.spacing3,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors['Grayiron/50'],
    marginBottom: metrics.spacing3,
  },
  merchantLogo: {
    width: 80,
    height: 80,
    borderRadius: 10,
    marginRight: metrics.spacing2,
    backgroundColor: colors['Grayiron/200'],
  },
  merchantDetails: {
    flex: 1,
  },
  merchantName: {
    fontSize: fonts.size.h3,
    fontWeight: '700',
    color: colors['Grayiron/700'],
    marginBottom: metrics.spacing1,
  },
  merchantAddress: {
    fontSize: fonts.size.medium,
    color: colors['Grayiron/500'],
    marginBottom: metrics.spacing2,
    lineHeight: 20,
  },
  merchantStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: metrics.spacing2,
  },
  merchantStat: {
    fontSize: fonts.size.small,
    color: colors['Grayiron/600'],
    backgroundColor: colors['Grayiron/50'],
    paddingHorizontal: metrics.spacing2,
    paddingVertical: metrics.spacing1,
    borderRadius: metrics.radius1,
  },
  tabContainer: {
    marginBottom: metrics.spacing3,
  },
  tabScroll: {
    paddingHorizontal: metrics.spacing3,
    gap: metrics.spacing2,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: metrics.spacing3,
    paddingVertical: metrics.spacing2,
    borderRadius: metrics.radius2,
    backgroundColor: colors['Grayiron/50'],
    minWidth: 120,
  },
  activeTab: {
    backgroundColor: colors['Primary/500'],
  },
  pressedTab: {
    opacity: 0.8,
  },
  tabIcon: {
    marginRight: metrics.spacing1,
  },
  tabText: {
    fontSize: fonts.size.small,
    fontWeight: '500',
    color: colors['Grayiron/700'],
  },
  activeTabText: {
    color: colors.white,
    fontWeight: '600',
  },
});

export default MerchantScreen;
