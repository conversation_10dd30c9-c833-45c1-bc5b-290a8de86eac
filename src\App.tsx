import 'react-native-gesture-handler';
import React, { useEffect } from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import MainNavigation from './navigation/navigation';
import Toast from 'react-native-toast-message';
import { CartProvider } from './hooks/contexts/CartContext';

const App = () => {
  return (
    <SafeAreaProvider>
      <CartProvider>
        <MainNavigation />
        <Toast topOffset={50} />
      </CartProvider>
    </SafeAreaProvider>
  );
};

export default App;
