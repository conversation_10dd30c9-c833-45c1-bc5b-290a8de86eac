import React, { useEffect, useState, useRef } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Easing,
  TextInput,
  ScrollView,
  Platform,
} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import colors from 'themes/colors';
import fonts from 'themes/fonts';
import metrics from 'themes/metrics';

type AlertType = 'success' | 'error' | 'warning' | 'info' | 'confirm' | 'radio';

interface AlertButton {
  text: string;
  onPress?: (selectedReason?: string) => void;
  style?: 'default' | 'cancel' | 'destructive';
  accessibilityLabel?: string;
}

interface RadioOption {
  label: string;
  value: string;
}

interface CustomAlertProps {
  visible: boolean;
  type?: AlertType;
  title: string;
  message?: string;
  buttons?: AlertButton[];
  onDismiss?: () => void;
  dismissable?: boolean;
  showInput?: boolean;
  inputPlaceholder?: string;
  onInputChange?: (text: string) => void;
  radioOptions?: RadioOption[];
  onRadioSelect?: (value: string) => void;
}

const CustomAlert: React.FC<CustomAlertProps> = ({
  visible,
  type = 'info',
  title,
  message,
  buttons = [],
  onDismiss,
  dismissable = true,
  showInput = false,
  inputPlaceholder = '',
  onInputChange,
  radioOptions = [],
  onRadioSelect,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const [selectedRadio, setSelectedRadio] = useState<string | null>(null);

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          friction: 8,
          tension: 40,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.9,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
    return () => {
      fadeAnim.setValue(0);
      scaleAnim.setValue(0.9);
    };
  }, [visible, fadeAnim, scaleAnim]);

  const handleBackdropPress = () => {
    if (dismissable && onDismiss) {
      onDismiss();
    }
  };

  const getIconAndColor = (): { icon: string; color: string } => {
    switch (type) {
      case 'success':
        return { icon: 'check-circle-outline', color: colors['Success/500'] };
      case 'error':
        return { icon: 'close-circle-outline', color: colors['Danger/500'] };
      case 'warning':
        return { icon: 'alert-circle-outline', color: colors['Warning/500'] };
      case 'confirm':
        return { icon: 'help-circle-outline', color: colors['Primary/600'] };
      case 'radio':
        return { icon: 'format-list-bulleted', color: colors['Primary/600'] };
      case 'info':
      default:
        return { icon: 'information-outline', color: colors['Primary/600'] };
    }
  };

  const { icon, color } = getIconAndColor();

  const handleRadioSelect = (value: string) => {
    setSelectedRadio(value);
    onRadioSelect?.(value);
  };

  const renderButton = (button: AlertButton, index: number) => {
    const isDestructive = button.style === 'destructive';
    const isCancel = button.style === 'cancel';
    return (
      <TouchableOpacity
        key={index}
        style={[
          styles.button,
          {
            backgroundColor: isDestructive
              ? colors['Danger/50']
              : isCancel
              ? colors['Gray/100']
              : colors['Primary/50'],
            borderColor: isDestructive
              ? colors['Danger/200']
              : isCancel
              ? colors['Gray/200']
              : colors['Primary/200'],
          },
        ]}
        onPress={() => button.onPress?.(selectedRadio)}
        activeOpacity={0.7}
        accessibilityRole="button"
        accessibilityLabel={button.accessibilityLabel || button.text}
      >
        <Text
          style={[
            styles.buttonText,
            {
              color: isDestructive
                ? colors['Danger/600']
                : isCancel
                ? colors['Gray/600']
                : colors['Primary/600'],
            },
          ]}
        >
          {button.text}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderRadioOption = (option: RadioOption, index: number) => (
    <TouchableOpacity
      key={index}
      style={styles.radioContainer}
      onPress={() => handleRadioSelect(option.value)}
      activeOpacity={0.7}
      accessibilityRole="radio"
      accessibilityState={{ checked: selectedRadio === option.value }}
    >
      <View style={styles.radioCircle}>
        {selectedRadio === option.value && (
          <View style={styles.selectedRadio} />
        )}
      </View>
      <Text style={styles.radioLabel}>{option.label}</Text>
    </TouchableOpacity>
  );

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={handleBackdropPress}
      hardwareAccelerated
    >
      <View style={styles.overlay}>
        <Animated.View
          style={[
            styles.container,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <View style={[styles.iconContainer, { backgroundColor: `${color}15` }]}>
            <MaterialCommunityIcons name={icon} size={28} color={color} />
          </View>
          <Text style={styles.title}>{title}</Text>
          {message && <Text style={styles.message}>{message}</Text>}
          {showInput && (
            <TextInput
              style={styles.input}
              placeholder={inputPlaceholder}
              placeholderTextColor={colors['Gray/400']}
              onChangeText={onInputChange}
              multiline={type !== 'confirm'}
              numberOfLines={type === 'confirm' ? 1 : 3}
              keyboardType={type === 'confirm' ? 'numeric' : 'default'}
              maxLength={type === 'confirm' ? 8 : undefined}
              autoFocus
              accessibilityLabel="Input field"
            />
          )}
          {type === 'radio' && radioOptions.length > 0 && (
            <ScrollView
              style={styles.radioList}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ paddingBottom: metrics.spacing2 }}
            >
              {radioOptions.map(renderRadioOption)}
            </ScrollView>
          )}
          <View style={styles.buttonContainer}>
            {buttons.length > 0 ? (
              buttons.map(renderButton)
            ) : (
              <TouchableOpacity
                style={[styles.button, { backgroundColor: colors['Primary/50'], borderColor: colors['Primary/200'] }]}
                onPress={onDismiss}
                activeOpacity={0.7}
                accessibilityRole="button"
                accessibilityLabel="Close alert"
              >
                <Text style={[styles.buttonText, { color: colors['Primary/600'] }]}>Đóng</Text>
              </TouchableOpacity>
            )}
          </View>
          {dismissable && (
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onDismiss}
              activeOpacity={0.7}
              accessibilityRole="button"
              accessibilityLabel="Close alert"
            >
              <MaterialCommunityIcons name="close" size={20} color={colors['Gray/500']} />
            </TouchableOpacity>
          )}
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: metrics.spacing3,
  },
  container: {
    backgroundColor: colors.white,
    borderRadius: metrics.radius3,
    padding: metrics.spacing3,
    width: '90%',
    maxWidth: 360,
    alignItems: 'center',
    shadowColor: colors['Gray/800'],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: metrics.spacing2,
  },
  title: {
    ...fonts.style.h3,
    fontWeight: '600',
    color: colors['Gray/800'],
    textAlign: 'center',
    marginBottom: metrics.spacing2,
  },
  message: {
      ...fonts.style.normal,
    color: colors['Gray/600'],
    textAlign: 'center',
    marginBottom: metrics.spacing3,
    lineHeight: 20,
  },
  input: {
    width: '100%',
    borderWidth: 1,
    borderColor: colors['Gray/200'],
    borderRadius: metrics.radius2,
    padding: metrics.spacing2,
    marginBottom: metrics.spacing3,
     ...fonts.style.normal,
    color: colors['Gray/800'],
    backgroundColor: colors['Gray/50'],
    textAlignVertical: 'top',
  },
  radioList: {
    width: '100%',
    maxHeight: 160,
    marginBottom: metrics.spacing3,
  },
  radioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: metrics.spacing1,
  },
  radioCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors['Primary/600'],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: metrics.spacing1,
  },
  selectedRadio: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors['Primary/600'],
  },
  radioLabel: {
     ...fonts.style.normal,
    color: colors['Gray/800'],
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
    gap: metrics.spacing2,
  },
  button: {
    flex: 1,
    minWidth: 100,
    maxWidth: 150,
    paddingVertical: metrics.spacing2,
    borderRadius: metrics.radius2,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
     ...fonts.style.normal,
    fontWeight: '600',
    fontSize: 16,
  },
  closeButton: {
    position: 'absolute',
    top: metrics.spacing2,
    right: metrics.spacing2,
    padding: metrics.spacing1,
  },
});

export default CustomAlert;