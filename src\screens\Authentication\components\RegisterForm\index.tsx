import React, { useState } from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { CheckBox } from '@ui-kitten/components';
import Icon from 'react-native-vector-icons/MaterialIcons';

import CommonButton from 'components/CommonButton';
import { AUTHENTICATION_STEP, REGEX_INPUT } from 'constants/authentication';
import FormInput from 'components/FormInput';
import { sendOtp, signUp } from 'api/users';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import CustomAlert from 'utils/widgets/CustomAlert';
import { Toast } from 'react-native-toast-message/lib/src/Toast';

// Schema cho bước 1: Nhập tất cả thông tin
const registerSchema = yup.object({
  phoneNo: yup
    .string()
    .required('Vui lòng nhập số điện thoại')
    .trim()
    .matches(REGEX_INPUT.PHONE, 'Số điện thoại chưa đúng định dạng'),
  firstname: yup
    .string()
    .required('Vui lòng nhập tên')
    .trim(),
  lastname: yup
    .string()
    .required('Vui lòng nhập họ')
    .trim(),
  email: yup
    .string()
    .required('Vui lòng nhập email')
    .trim()
    .matches(REGEX_INPUT.EMAIL, 'Email chưa đúng định dạng'),
  password: yup
    .string()
    .required('Vui lòng nhập mật khẩu')
    .trim()
    .min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
  acceptTerm: yup
    .boolean()
    .isTrue('Vui lòng chấp nhận điều khoản của chúng tôi'),
});

const RegisterForm = ({ setStep, currentData, setCurrentData }: any) => {
  const [alertVisible, setAlertVisible] = useState(false);
  const [otp, setOtp] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  // Form cho bước 1
  const {
    control,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm({
    defaultValues: {
      phoneNo: '',
      firstname: '',
      lastname: '',
      email: '',
      password: '',
      acceptTerm: undefined,
    },
    resolver: yupResolver(registerSchema),
  });

  // Xử lý gửi OTP và hiển thị alert
  const onSubmit = async (values: any) => {
    try {
      const response = await sendOtp({
        phoneNumber: values.phoneNo,
      });
      if (response?.code) {
        setError('phoneNo', {
          message: response.message || 'Có lỗi xảy ra trong quá trình gửi mã OTP',
        });
      } else {
        setCurrentData({
          ...currentData,
          phoneNo: values.phoneNo,
          firstname: values.firstname,
          lastname: values.lastname,
          email: values.email,
          password: values.password,
        });
        setAlertVisible(true);
      }
    } catch (error) {
      setError('phoneNo', {
        message: 'Có lỗi xảy ra trong quá trình gửi mã OTP',
      });
    }
  };

  // Xử lý xác nhận OTP và gọi API đăng ký
  const handleOtpSubmit = async () => {
    try {
      const signupData = {
        firstname: currentData.firstname,
        lastname: currentData.lastname,
        phoneNo: currentData.phoneNo,
        email: currentData.email,
        password: currentData.password,
        roles: ['USER'],
        origin: 'https://hathyo.com',
        otp: otp,
      };
      const response = await signUp({ body: signupData });
      if (response?.code) {
        setError('phoneNo', {
          message: response.message || 'Mã OTP không hợp lệ',
        });
      } else {
        setAlertVisible(false); // Ẩn popup sau khi xác nhận OTP thành công
        setOtp(''); // Reset OTP input
        Toast.show({
                type: 'success',
                text1: 'Đăng nhập thành công',
                position: 'top',
              });
        setStep(AUTHENTICATION_STEP.LOGIN);
      }
    } catch (error) {
      setError('phoneNo', {
        message: 'Có lỗi xảy ra trong quá trình đăng ký',
      });
    }
  };

  // Toggle show/hide password
  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <View style={styles.container}>
      {/* Form bước 1: Nhập tất cả thông tin */}
      <View style={styles.content}>
        <View>
          <Text style={styles.subTitle}>Đăng ký</Text>
        </View>
        <View style={styles.item}>
          <FormInput
            label="Số điện thoại"
            control={control}
            required
            placeholder="Nhập số điện thoại"
            name="phoneNo"
            errors={errors?.phoneNo}
            keyboardType="phone-pad"
          />
        </View>
        <View style={styles.item}>
          <FormInput
            label="Tên"
            control={control}
            required
            placeholder="Nhập tên"
            name="firstname"
            errors={errors?.firstname}
          />
        </View>
        <View style={styles.item}>
          <FormInput
            label="Họ"
            control={control}
            required
            placeholder="Nhập họ"
            name="lastname"
            errors={errors?.lastname}
          />
        </View>
        <View style={styles.item}>
          <FormInput
            label="Email"
            control={control}
            required
            placeholder="Nhập email"
            name="email"
            errors={errors?.email}
            keyboardType="email-address"
          />
        </View>
        <View style={styles.item}>
          <FormInput
            label="Mật khẩu"
            control={control}
            required
            placeholder="Nhập mật khẩu"
            name="password"
            errors={errors?.password}
            secureTextEntry={!showPassword}
            rightIcon={
              <TouchableOpacity onPress={toggleShowPassword}>
                <Icon
                  name={showPassword ? 'visibility-off' : 'visibility'}
                  size={24}
                  color={colors['Grayiron/400']}
                />
              </TouchableOpacity>
            }
          />
        </View>
        <View style={styles.item}>
          <Controller
            control={control}
            rules={{ required: true }}
            render={({ field: { onChange, onBlur, value } }) => (
              <CheckBox
                checked={!!value}
                status="basic"
                onBlur={onBlur}
                onChange={onChange}>
                Tôi đã đọc và đồng ý với Điều khoản sử dụng và Chính sách bảo
                mật thông tin cá nhân của Hathyo.com
              </CheckBox>
            )}
            name="acceptTerm"
          />
          {errors?.acceptTerm && (
            <Text style={commonStyles.errorText}>
              {errors.acceptTerm.message}
            </Text>
          )}
        </View>
        <View style={commonStyles.rowCenter}>
          <CommonButton
            onPress={handleSubmit(onSubmit)}
            customStyles={{ backgroundColor: colors['Moss/500'] }}
          >
            Đăng ký
          </CommonButton>
        </View>
      </View>
      <View style={commonStyles.rowCenter}>
        <TouchableOpacity onPress={() => setStep(AUTHENTICATION_STEP.LOGIN)}>
          <Text style={styles.text}>
            Đã có tài khoản ?
            <Text style={styles.registerText}> Đăng nhập ngay</Text>
          </Text>
        </TouchableOpacity>
      </View>

      {/* Alert để nhập OTP */}
      <CustomAlert
        visible={alertVisible}
        type="confirm"
        title="Nhập mã OTP"
        message="Vui lòng nhập mã OTP đã được gửi đến số điện thoại của bạn."
        showInput={true}
        inputPlaceholder="Nhập mã OTP"
        onInputChange={setOtp}
        buttons={[
          {
            text: 'Hủy',
            style: 'cancel',
            onPress: () => {
              setAlertVisible(false);
              setOtp('');
            },
          },
          {
            text: 'Xác nhận',
            style: 'default',
            onPress: handleOtpSubmit,
          },
        ]}
        onDismiss={() => {
          setAlertVisible(false);
          setOtp('');
        }}
        dismissable={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
  },
  content: {
    padding: metrics.spacing4,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
    marginBottom: metrics.spacing4,
  },
  item: {
    marginBottom: metrics.spacing3,
  },
  subTitle: {
    fontSize: fonts.size.h2,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
  text: {
    fontSize: fonts.size.input,
    fontWeight: '400',
    color: colors['Grayiron/400'],
    marginBottom: metrics.spacing4,
  },
  registerText: {
    fontSize: fonts.size.input,
    fontWeight: '500',
    color: colors['Moss/500'],
    marginBottom: metrics.spacing4,
  },
});

export default RegisterForm;